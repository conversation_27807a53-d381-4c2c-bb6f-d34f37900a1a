
<x-layouts.app.frontend :title="__('Desiree Swing Club')">
<div class="bg-gradient-to-br from-pink-600 via-fuchsia-500 to-yellow-400 text-white py-20 text-center">
    <h1 class="text-4xl md:text-6xl font-bold mb-4">Desire<PERSON></h1>
    <p class="text-xl md:text-2xl max-w-3xl mx-auto">A nova era das conexões reais: eventos, matchs, lives, e-commerce e muito mais em um só lugar.</p>
    <a href="{{ route('register') }}" class="mt-6 inline-block px-8 py-3 bg-white text-pink-600 font-bold rounded-full hover:bg-gray-100 transition">Cadastre-se agora</a>
</div>

{{-- Seção: Por que escolher Desiree --}}
<section class="py-16 bg-white text-gray-800">
    <div class="max-w-6xl mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12">Por que escolher a <PERSON>?</h2>
        <div class="grid md:grid-cols-3 gap-8 text-center">
            <div>
                <h3 class="text-xl font-bold text-pink-600 mb-2">Matchs inteligentes</h3>
                <p>Encontre pessoas com interesses parecidos com você. Rápido, simples, divertido — tipo Tinder, só que melhor.</p>
            </div>
            <div>
                <h3 class="text-xl font-bold text-pink-600 mb-2">Carteira digital</h3>
                <p>Receba, pague e movimente seu dinheiro direto no app. Segurança, praticidade e controle total nas suas mãos.</p>
            </div>
            <div>
                <h3 class="text-xl font-bold text-pink-600 mb-2">Eventos exclusivos</h3>
                <p>Participe ou crie eventos pagos ou gratuitos com check-in digital, pagamentos online e controle de lista.</p>
            </div>
        </div>
    </div>
</section>

{{-- Seção: Funcionalidades destaque --}}
<section class="py-16 bg-gray-100">
    <div class="max-w-6xl mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12">Tudo em um só lugar</h2>
        <div class="grid md:grid-cols-2 gap-10">
            <div>
                <ul class="space-y-4 text-lg">
                    <li><strong>💬 Chat em tempo real:</strong> mensagens com entrega e leitura instantânea.</li>
                    <li><strong>🛍️ Loja integrada:</strong> venda de produtos físicos e digitais com pagamentos online.</li>
                    <li><strong>📺 Streaming ao vivo:</strong> lives com chat, monetização e gravação automática.</li>
                    <li><strong>🎮 Gamificação:</strong> conquiste pontos, suba de nível e desbloqueie vantagens.</li>
                    <li><strong>📌 Grupos & comunidades:</strong> crie ou entre em grupos com conteúdo privado.</li>
                    <li><strong>📖 Contos e histórias:</strong> publique e compartilhe sua criatividade.</li>
                </ul>
            </div>
            <div>
                <img src="/images/mockup-app.png" alt="App Desiree" class="rounded-xl shadow-xl">
            </div>
        </div>
    </div>
</section>

{{-- Seção: Visual e experiência --}}
<section class="py-16 bg-white text-gray-800">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <h2 class="text-3xl font-bold mb-6">Design moderno, experiência envolvente</h2>
        <p class="text-lg mb-8">Interface neon, modo escuro, animações suaves e navegação intuitiva para tornar sua experiência divertida e funcional em qualquer dispositivo.</p>
        <img src="/images/interface-preview.png" alt="Preview Interface" class="mx-auto rounded-lg shadow-md">
    </div>
</section>

{{-- Seção: Comece agora --}}
<section class="py-20 bg-pink-600 text-white text-center">
    <div class="max-w-3xl mx-auto px-6">
        <h2 class="text-4xl font-bold mb-6">Pronto para revolucionar sua presença digital?</h2>
        <p class="text-lg mb-8">Cadastre-se e explore tudo que a Desiree oferece — conexões reais, oportunidades e experiências únicas.</p>
        <a href="{{ route('register') }}" class="inline-block bg-white text-pink-600 font-bold px-10 py-3 rounded-full hover:bg-gray-100 transition">Quero fazer parte</a>
    </div>
</section>
</x-layouts.app.frontend>
